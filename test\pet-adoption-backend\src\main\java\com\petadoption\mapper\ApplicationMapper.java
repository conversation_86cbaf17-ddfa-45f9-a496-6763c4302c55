package com.petadoption.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.petadoption.entity.Application;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 申请Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface ApplicationMapper extends BaseMapper<Application> {

    /**
     * 根据用户ID分页查询申请
     */
    @Select("SELECT * FROM applications WHERE user_id = #{userId} ORDER BY create_time DESC")
    IPage<Application> findByUserId(Page<Application> page, @Param("userId") Long userId);

    /**
     * 根据宠物ID分页查询申请
     */
    @Select("SELECT * FROM applications WHERE pet_id = #{petId} ORDER BY create_time DESC")
    IPage<Application> findByPetId(Page<Application> page, @Param("petId") Long petId);

    /**
     * 根据状态分页查询申请
     */
    @Select("SELECT * FROM applications WHERE status = #{status} ORDER BY create_time DESC")
    IPage<Application> findByStatus(Page<Application> page, @Param("status") String status);

    /**
     * 检查用户是否已申请过该宠物
     */
    @Select("SELECT COUNT(*) FROM applications WHERE user_id = #{userId} AND pet_id = #{petId}")
    Long existsByUserIdAndPetId(@Param("userId") Long userId, @Param("petId") Long petId);

    /**
     * 根据审核人ID分页查询申请
     */
    @Select("SELECT * FROM applications WHERE reviewer_id = #{reviewerId} ORDER BY create_time DESC")
    IPage<Application> findByReviewerId(Page<Application> page, @Param("reviewerId") Long reviewerId);

    /**
     * 统计申请总数
     */
    @Select("SELECT COUNT(*) FROM applications")
    Long countAllApplications();

    /**
     * 统计待审核申请数量
     */
    @Select("SELECT COUNT(*) FROM applications WHERE status = 'pending'")
    Long countPendingApplications();

    /**
     * 统计已通过申请数量
     */
    @Select("SELECT COUNT(*) FROM applications WHERE status = 'approved'")
    Long countApprovedApplications();

    /**
     * 统计已拒绝申请数量
     */
    @Select("SELECT COUNT(*) FROM applications WHERE status = 'rejected'")
    Long countRejectedApplications();

    /**
     * 根据用户ID统计申请数量
     */
    @Select("SELECT COUNT(*) FROM applications WHERE user_id = #{userId}")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 根据宠物ID统计申请数量
     */
    @Select("SELECT COUNT(*) FROM applications WHERE pet_id = #{petId}")
    Long countByPetId(@Param("petId") Long petId);

    /**
     * 根据状态统计申请数量
     */
    @Select("SELECT COUNT(*) FROM applications WHERE status = #{status}")
    Long countByStatus(@Param("status") String status);

    /**
     * 根据创建时间范围统计申请数量
     */
    @Select("SELECT COUNT(*) FROM applications WHERE create_time BETWEEN #{startTime} AND #{endTime}")
    Long countByCreateTimeBetween(@Param("startTime") java.time.LocalDateTime startTime,
                                  @Param("endTime") java.time.LocalDateTime endTime);

    /**
     * 根据审核时间范围统计已通过申请数量
     */
    @Select("SELECT COUNT(*) FROM applications WHERE status = 'approved' AND review_time BETWEEN #{startTime} AND #{endTime}")
    Long countApprovedByReviewTimeBetween(@Param("startTime") java.time.LocalDateTime startTime,
                                          @Param("endTime") java.time.LocalDateTime endTime);

    /**
     * 检查用户是否已申请过该宠物
     */
    @Select("SELECT COUNT(*) FROM applications WHERE user_id = #{userId} AND pet_id = #{petId}")
    Long existsByUserIdAndPetId(@Param("userId") Long userId, @Param("petId") Long petId);
}
